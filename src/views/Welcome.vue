<template>
  <div class="welcome-container">
    <!-- 动态背景 -->
    <div class="background-animation">
      <div class="red-particles"></div>
      <div class="tech-grid"></div>
      <div class="floating-elements">
        <div class="element party-emblem">☭</div>
        <div class="element star">★</div>
        <div class="element gear">⚙</div>
        <div class="element circuit">◉</div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="welcome-content">
      <div class="header">
        <div class="logo-section">
          <div class="logo">
            <i class="fas fa-graduation-cap"></i>
          </div>
          <h1 class="title">成都开放大学</h1>
          <h2 class="subtitle">思政AI对话平台</h2>
        </div>
      </div>

      <div class="description">
        <div class="feature-cards">
          <div class="feature-card">
            <div class="feature-icon">
              <i class="fas fa-book"></i>
            </div>
            <h3>智能知识库系统</h3>
            <p>预装四大思政教材电子知识库，支持教材内容的智能问答</p>
          </div>
          
          <div class="feature-card">
            <div class="feature-icon">
              <i class="fas fa-users"></i>
            </div>
            <h3>多角色对话模式</h3>
            <p>内置5种虚拟人物角色，每个角色具有独立的回答风格</p>
          </div>
          
          <div class="feature-card">
            <div class="feature-icon">
              <i class="fas fa-search"></i>
            </div>
            <h3>精准知识定位</h3>
            <p>提供知识点在教材中的精确章节定位和原文内容</p>
          </div>
          
          <div class="feature-card">
            <div class="feature-icon">
              <i class="fas fa-lightbulb"></i>
            </div>
            <h3>知识点提取功能</h3>
            <p>支持独立的知识点提取模式，精准罗列核心知识点条目</p>
          </div>
        </div>
      </div>

      <div class="action-section">
        <button class="start-btn" @click="startChat">
          <i class="fas fa-comments"></i>
          开始对话
        </button>
        <p class="welcome-text">
          欢迎使用思政AI对话平台，让我们一起探索思政教育的智慧之光
        </p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Welcome',
  methods: {
    startChat() {
      console.log('开始对话按钮被点击')
      this.$router.push('/chat')
    }
  }
}
</script>

<style scoped>
.welcome-container {
  position: relative;
  width: 100%;
  min-height: 100vh;
  overflow-x: hidden;
  overflow-y: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #d32f2f 0%, #b71c1c 50%, #8b0000 100%);
  padding: 20px 0;
}

/* 动态背景 */
.background-animation {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.red-particles {
  position: absolute;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(2px 2px at 20px 30px, rgba(255, 255, 255, 0.3), transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(255, 255, 255, 0.2), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(255, 255, 255, 0.4), transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(255, 255, 255, 0.3), transparent);
  background-repeat: repeat;
  background-size: 200px 100px;
  animation: particleMove 20s linear infinite;
}

.tech-grid {
  position: absolute;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: gridMove 30s linear infinite;
}

.floating-elements {
  position: absolute;
  width: 100%;
  height: 100%;
}

.element {
  position: absolute;
  font-size: 24px;
  color: rgba(255, 255, 255, 0.6);
  animation: float 6s ease-in-out infinite;
}

.party-emblem {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.star {
  top: 30%;
  right: 15%;
  animation-delay: 1.5s;
}

.gear {
  bottom: 30%;
  left: 20%;
  animation-delay: 3s;
}

.circuit {
  bottom: 20%;
  right: 25%;
  animation-delay: 4.5s;
}

/* 主要内容 */
.welcome-content {
  position: relative;
  z-index: 2;
  text-align: center;
  color: white;
  max-width: 1200px;
  width: 100%;
  padding: 40px 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: calc(100vh - 40px);
}

.header {
  margin-bottom: 40px;
  flex-shrink: 0;
}

.logo-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.logo {
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36px;
  color: white;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.title {
  font-size: 48px;
  font-weight: 700;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.subtitle {
  font-size: 24px;
  font-weight: 400;
  margin: 0;
  opacity: 0.9;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.description {
  margin-bottom: 40px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.feature-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-top: 30px;
  max-width: 1000px;
  margin-left: auto;
  margin-right: auto;
}

.feature-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 32px 24px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-8px);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.2);
}

.feature-icon {
  font-size: 36px;
  margin-bottom: 16px;
  color: #ffd700;
}

.feature-card h3 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 12px;
  color: white;
}

.feature-card p {
  font-size: 14px;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
}

.action-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  flex-shrink: 0;
  margin-top: auto;
}

.start-btn {
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  color: #d32f2f;
  border: none;
  padding: 16px 48px;
  font-size: 18px;
  font-weight: 600;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 12px;
  box-shadow: 0 8px 24px rgba(255, 215, 0, 0.3);
  position: relative;
  z-index: 10;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.start-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 32px rgba(255, 215, 0, 0.4);
  background: linear-gradient(45deg, #ffed4e, #ffd700);
}

.welcome-text {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  max-width: 600px;
  line-height: 1.6;
}

/* 动画 */
@keyframes particleMove {
  0% { transform: translateY(0px); }
  100% { transform: translateY(-100px); }
}

@keyframes gridMove {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .welcome-container {
    padding: 10px 0;
  }

  .welcome-content {
    padding: 20px 16px;
    min-height: calc(100vh - 20px);
  }

  .header {
    margin-bottom: 30px;
  }

  .description {
    margin-bottom: 30px;
  }

  .title {
    font-size: 32px;
  }

  .subtitle {
    font-size: 18px;
  }

  .feature-cards {
    grid-template-columns: 1fr;
    gap: 16px;
    margin-top: 20px;
  }

  .feature-card {
    padding: 20px 16px;
  }

  .feature-card h3 {
    font-size: 18px;
  }

  .feature-card p {
    font-size: 14px;
  }

  .start-btn {
    padding: 14px 36px;
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .welcome-content {
    padding: 15px 12px;
  }

  .title {
    font-size: 24px;
  }

  .subtitle {
    font-size: 16px;
  }

  .logo {
    width: 60px;
    height: 60px;
    font-size: 28px;
  }

  .feature-card {
    padding: 16px 12px;
  }

  .feature-icon {
    font-size: 28px;
  }

  .start-btn {
    padding: 12px 24px;
    font-size: 15px;
  }

  .welcome-text {
    font-size: 14px;
  }
}
</style>
