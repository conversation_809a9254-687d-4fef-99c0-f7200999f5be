<template>
  <div class="chat-container">
    <header class="chat-header">
      <div class="header-content">
        <div class="logo-section">
          <button class="back-btn" @click="goBack">
            <i class="fas fa-arrow-left"></i>
          </button>
          <div class="logo">
            <i class="fas fa-graduation-cap"></i>
          </div>
          <div class="title-section">
            <h1 class="title">思政AI对话平台</h1>
            <p class="subtitle">成都开放大学</p>
          </div>
        </div>
        <div class="current-role">
          <div class="role-avatar">{{ currentRole.avatar }}</div>
          <div class="role-info">
            <span class="role-name">{{ currentRole.name }}</span>
            <span class="role-desc">{{ currentRole.description }}</span>
          </div>
        </div>
      </div>
    </header>

    <main class="chat-main">
      <div class="messages-container" ref="messagesContainer">
        <div class="messages-list">
          <div class="welcome-message" v-if="messages.length === 0">
            <div class="welcome-content">
              <div class="welcome-avatar">{{ currentRole.avatar }}</div>
              <div class="welcome-text">
                <h3>您好！我是{{ currentRole.name }}</h3>
                <p>{{ currentRole.description }}</p>
                <p class="welcome-tip">您可以尝试问我："{{ currentRole.initQuestion }}"</p>
              </div>
            </div>
          </div>

          <MessageItem
            v-for="message in messages"
            :key="message.id"
            :message="message"
            @show-sources="showSources"
          />

          <div class="loading-message" v-if="isLoading">
            <div class="message-item ai-message">
              <div class="message-avatar">{{ currentRole.avatar }}</div>
              <div class="message-content">
                <div class="message-header">
                  <span class="sender-name">{{ currentRole.name }}</span>
                  <span class="message-time">正在思考中...</span>
                </div>
                <div class="message-body">
                  <div class="typing-indicator">
                    <span></span>
                    <span></span>
                    <span></span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <footer class="chat-footer">
      <div class="input-container">
        <div class="input-wrapper">
          <textarea
            v-model="inputMessage"
            @keydown="handleKeydown"
            @input="handleInput"
            placeholder="请输入您的问题..."
            class="message-input"
            rows="1"
            ref="messageInput"
            :disabled="isLoading"
          ></textarea>
          <div class="input-actions">
            <span class="char-count" :class="{ 'over-limit': inputMessage.length > 100 }">
              {{ inputMessage.length }}/100
            </span>
            <button
              class="send-btn"
              @click="sendMessage"
              :disabled="!canSend"
            >
              <i class="fas fa-paper-plane"></i>
            </button>
          </div>
        </div>

        <div class="footer-actions">
          <button class="role-switch-btn" @click="showRoleSelector">
            <i class="fas fa-user-friends"></i>
            切换角色
          </button>
        </div>
      </div>
    </footer>

    <RoleSelector
      v-if="showRoleModal"
      :current-role="currentRole"
      @select-role="selectRole"
      @close="showRoleModal = false"
    />

    <SourceModal
      v-if="showSourceModal"
      :sources="selectedSources"
      @close="showSourceModal = false"
    />
  </div>
</template>

<script>
import MessageItem from '../components/MessageItem.vue'
import RoleSelector from '../components/RoleSelector.vue'
import SourceModal from '../components/SourceModal.vue'
import { sendChatMessage } from '../utils/api'
import { ROLES, getCurrentTime, formatTime } from '../utils/index'

export default {
  name: 'Chat',
  components: {
    MessageItem,
    RoleSelector,
    SourceModal
  },
  data() {
    return {
      messages: [],
      inputMessage: '',
      isLoading: false,
      currentRole: ROLES.TEACHER,
      showRoleModal: false,
      showSourceModal: false,
      selectedSources: []
    }
  },
  computed: {
    canSend() {
      return this.inputMessage.trim().length > 0 &&
             this.inputMessage.length <= 100 &&
             !this.isLoading
    }
  },
  methods: {
    goBack() {
      this.$router.push('/')
    },

    handleKeydown(event) {
      if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault()
        this.sendMessage()
      }
    },

    handleInput() {
      this.autoResizeTextarea()
    },

    autoResizeTextarea() {
      const textarea = this.$refs.messageInput
      if (textarea) {
        textarea.style.height = 'auto'
        textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px'
      }
    },

    async sendMessage() {
      if (!this.canSend) return

      const message = this.inputMessage.trim()
      this.inputMessage = ''
      this.autoResizeTextarea()

      const userMessage = {
        id: Date.now(),
        type: 'user',
        content: message,
        timestamp: getCurrentTime(),
        formattedTime: formatTime(new Date())
      }
      this.messages.push(userMessage)

      this.$nextTick(() => {
        this.scrollToBottom()
      })

      this.isLoading = true
      try {
        const response = await sendChatMessage(this.currentRole.key, message)

        const aiMessage = {
          id: Date.now() + 1,
          type: 'ai',
          content: response.result,
          sources: response.sources || [],
          role: this.currentRole,
          timestamp: getCurrentTime(),
          formattedTime: formatTime(new Date())
        }
        this.messages.push(aiMessage)

        this.$nextTick(() => {
          this.scrollToBottom()
        })
      } catch (error) {
        const errorMessage = {
          id: Date.now() + 1,
          type: 'ai',
          content: `抱歉，发生了错误：${error.message}`,
          sources: [],
          role: this.currentRole,
          timestamp: getCurrentTime(),
          formattedTime: formatTime(new Date()),
          isError: true
        }
        this.messages.push(errorMessage)

        this.$nextTick(() => {
          this.scrollToBottom()
        })
      } finally {
        this.isLoading = false
      }
    },

    scrollToBottom() {
      const container = this.$refs.messagesContainer
      if (container) {
        container.scrollTop = container.scrollHeight
      }
    },

    showRoleSelector() {
      this.showRoleModal = true
    },

    selectRole(role) {
      this.currentRole = role
      this.showRoleModal = false

      this.inputMessage = role.initQuestion
      this.$nextTick(() => {
        this.autoResizeTextarea()
        this.$refs.messageInput.focus()
      })
    },

    showSources(sources) {
      this.selectedSources = sources
      this.showSourceModal = true
    }
  },

  mounted() {
    this.$refs.messageInput.focus()
  }
}
</script>

<style scoped>
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f5f5f5;
}

.chat-header {
  background: white;
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-light);
  z-index: 10;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-btn {
  background: none;
  border: none;
  font-size: 18px;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.back-btn:hover {
  background: var(--background-gray);
  color: var(--primary-red);
}

.logo {
  width: 40px;
  height: 40px;
  background: var(--primary-red);
  color: white;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.title-section .title {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.title-section .subtitle {
  font-size: 12px;
  color: var(--text-secondary);
  margin: 0;
}

.current-role {
  display: flex;
  align-items: center;
  gap: 12px;
  background: var(--background-gray);
  padding: 8px 16px;
  border-radius: 20px;
}

.role-avatar {
  font-size: 20px;
}

.role-info {
  display: flex;
  flex-direction: column;
}

.role-name {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
}

.role-desc {
  font-size: 12px;
  color: var(--text-secondary);
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.chat-main {
  flex: 1;
  overflow: hidden;
}

.messages-container {
  height: 100%;
  overflow-y: auto;
  padding: 20px;
}

.messages-list {
  max-width: 800px;
  margin: 0 auto;
}

.welcome-message {
  margin-bottom: 32px;
}

.welcome-content {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  background: white;
  padding: 24px;
  border-radius: 16px;
  box-shadow: var(--shadow-light);
}

.welcome-avatar {
  font-size: 32px;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--background-gray);
  border-radius: 50%;
  flex-shrink: 0;
}

.welcome-text h3 {
  margin: 0 0 8px 0;
  color: var(--text-primary);
  font-size: 18px;
}

.welcome-text p {
  margin: 0 0 8px 0;
  color: var(--text-secondary);
  line-height: 1.6;
}

.welcome-tip {
  color: var(--primary-red) !important;
  font-weight: 500;
}

.loading-message {
  margin-bottom: 20px;
}

.message-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 20px;
}

.ai-message {
  justify-content: flex-start;
}

.message-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  background: var(--background-gray);
  flex-shrink: 0;
}

.message-content {
  flex: 1;
  max-width: 70%;
}

.message-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.sender-name {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
}

.message-time {
  font-size: 12px;
  color: var(--text-light);
}

.message-body {
  background: white;
  padding: 16px;
  border-radius: 12px;
  box-shadow: var(--shadow-light);
}

.typing-indicator {
  display: flex;
  gap: 4px;
  align-items: center;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--text-light);
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) { animation-delay: -0.32s; }
.typing-indicator span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
  0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
  40% { transform: scale(1); opacity: 1; }
}

.chat-footer {
  background: white;
  border-top: 1px solid var(--border-color);
  padding: 16px 24px;
}

.input-container {
  max-width: 800px;
  margin: 0 auto;
}

.input-wrapper {
  display: flex;
  align-items: flex-end;
  gap: 12px;
  background: var(--background-gray);
  border-radius: 12px;
  padding: 12px 16px;
  margin-bottom: 12px;
}

.message-input {
  flex: 1;
  border: none;
  outline: none;
  background: none;
  font-size: 14px;
  line-height: 1.5;
  resize: none;
  min-height: 20px;
  max-height: 120px;
}

.input-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.char-count {
  font-size: 12px;
  color: var(--text-light);
}

.char-count.over-limit {
  color: var(--secondary-red);
}

.send-btn {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: none;
  background: var(--primary-red);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.send-btn:hover:not(:disabled) {
  background: var(--primary-red-dark);
  transform: scale(1.05);
}

.send-btn:disabled {
  background: var(--text-light);
  cursor: not-allowed;
}

.footer-actions {
  display: flex;
  justify-content: center;
}

.role-switch-btn {
  background: none;
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  padding: 8px 16px;
  border-radius: 20px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
}

.role-switch-btn:hover {
  border-color: var(--primary-red);
  color: var(--primary-red);
}

@media (max-width: 768px) {
  .header-content {
    padding: 12px 16px;
  }

  .role-desc {
    display: none;
  }

  .messages-container {
    padding: 16px;
  }

  .chat-footer {
    padding: 12px 16px;
  }

  .message-content {
    max-width: 85%;
  }
}
</style>