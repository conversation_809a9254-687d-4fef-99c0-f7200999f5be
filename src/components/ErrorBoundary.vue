<template>
  <div class="error-boundary" v-if="hasError">
    <div class="error-content">
      <div class="error-icon">
        <i class="fas fa-exclamation-triangle"></i>
      </div>
      <h3 class="error-title">出现了一些问题</h3>
      <p class="error-message">{{ errorMessage }}</p>
      <div class="error-actions">
        <button class="retry-btn" @click="retry">
          <i class="fas fa-redo"></i>
          重试
        </button>
        <button class="home-btn" @click="goHome">
          <i class="fas fa-home"></i>
          返回首页
        </button>
      </div>
    </div>
  </div>
  <slot v-else></slot>
</template>

<script>
export default {
  name: 'ErrorBoundary',
  data() {
    return {
      hasError: false,
      errorMessage: '系统遇到了未知错误，请稍后重试'
    }
  },
  methods: {
    retry() {
      this.hasError = false
      this.$emit('retry')
    },
    
    goHome() {
      this.$router.push('/')
    },
    
    handleError(error) {
      console.error('ErrorBoundary捕获到错误:', error)
      this.hasError = true
      this.errorMessage = error.message || '系统遇到了未知错误，请稍后重试'
    }
  },
  
  errorCaptured(error, instance, info) {
    console.error('Vue错误捕获:', error, info)
    this.handleError(error)
    return false
  }
}
</script>

<style scoped>
.error-boundary {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 40px 20px;
}

.error-content {
  text-align: center;
  max-width: 400px;
}

.error-icon {
  font-size: 48px;
  color: #ff9800;
  margin-bottom: 20px;
}

.error-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 12px 0;
}

.error-message {
  font-size: 16px;
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0 0 32px 0;
}

.error-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
}

.retry-btn,
.home-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
}

.retry-btn {
  background: var(--primary-red);
  color: white;
}

.retry-btn:hover {
  background: var(--primary-red-dark);
  transform: translateY(-1px);
}

.home-btn {
  background: white;
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
}

.home-btn:hover {
  background: var(--background-gray);
  color: var(--text-primary);
}

@media (max-width: 480px) {
  .error-actions {
    flex-direction: column;
  }
  
  .retry-btn,
  .home-btn {
    width: 100%;
    justify-content: center;
  }
}
</style>
