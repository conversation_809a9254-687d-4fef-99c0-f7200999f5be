<template>
  <div class="role-selector-overlay" @click="handleOverlayClick">
    <div class="role-selector-modal" @click.stop>
      <div class="modal-header">
        <h3 class="modal-title">选择对话角色</h3>
        <button class="close-btn" @click="close">
          <i class="fas fa-times"></i>
        </button>
      </div>
      
      <div class="modal-body">
        <div class="roles-grid">
          <div 
            v-for="role in roles" 
            :key="role.key"
            class="role-card"
            :class="{ 'active': role.key === currentRole.key }"
            @click="selectRole(role)"
          >
            <div class="role-avatar">
              {{ role.avatar }}
            </div>
            <div class="role-info">
              <h4 class="role-name">{{ role.name }}</h4>
              <p class="role-description">{{ role.description }}</p>
              <div class="role-example">
                <span class="example-label">示例问题：</span>
                <span class="example-text">"{{ role.initQuestion }}"</span>
              </div>
            </div>
            <div class="role-status" v-if="role.key === currentRole.key">
              <i class="fas fa-check-circle"></i>
            </div>
          </div>
        </div>
      </div>
      
      <div class="modal-footer">
        <button class="cancel-btn" @click="close">
          取消
        </button>
        <button 
          class="confirm-btn" 
          @click="confirmSelection"
          :disabled="!selectedRole"
        >
          确认选择
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { getRoleList } from '../utils/index'

export default {
  name: 'RoleSelector',
  props: {
    currentRole: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      roles: getRoleList(),
      selectedRole: null
    }
  },

  
  mounted() {
    // 设置当前角色为默认选中
    this.selectedRole = this.currentRole
    
    // 添加键盘事件监听
    document.addEventListener('keydown', this.handleKeydown)
  },
  
  beforeUnmount() {
    document.removeEventListener('keydown', this.handleKeydown)
  },
  
  methods: {
    handleOverlayClick() {
      this.close()
    },

    close() {
      this.$emit('close')
    },

    selectRole(role) {
      this.selectedRole = role
    },

    confirmSelection() {
      if (this.selectedRole) {
        this.$emit('select-role', this.selectedRole)
      }
    },

    handleKeydown(event) {
      if (event.key === 'Escape') {
        this.close()
      }
    }
  }
}
</script>

<style scoped>
.role-selector-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.role-selector-modal {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 800px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 24px 16px 24px;
  border-bottom: 1px solid var(--border-color);
}

.modal-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  color: var(--text-secondary);
  cursor: pointer;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: var(--background-gray);
  color: var(--text-primary);
}

.modal-body {
  padding: 24px;
  max-height: 60vh;
  overflow-y: auto;
}

.roles-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
}

.role-card {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 20px;
  border: 2px solid var(--border-color);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  background: white;
}

.role-card:hover {
  border-color: var(--primary-red);
  box-shadow: 0 4px 16px rgba(211, 47, 47, 0.1);
  transform: translateY(-2px);
}

.role-card.active {
  border-color: var(--primary-red);
  background: linear-gradient(135deg, #fff5f5, #ffebee);
  box-shadow: 0 4px 16px rgba(211, 47, 47, 0.15);
}

.role-card.selected-animation {
  animation: selectPulse 0.3s ease-out;
}

.role-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: var(--background-gray);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  flex-shrink: 0;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.role-card.active .role-avatar {
  background: linear-gradient(135deg, var(--primary-red), var(--primary-red-light));
  color: white;
  border-color: var(--primary-red-dark);
}

.role-info {
  flex: 1;
}

.role-name {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 8px 0;
}

.role-description {
  font-size: 14px;
  color: var(--text-secondary);
  line-height: 1.5;
  margin: 0 0 12px 0;
}

.role-example {
  background: var(--background-gray);
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 13px;
}

.example-label {
  color: var(--text-secondary);
  font-weight: 500;
}

.example-text {
  color: var(--primary-red);
  font-weight: 500;
}

.role-status {
  position: absolute;
  top: 16px;
  right: 16px;
  color: var(--primary-red);
  font-size: 20px;
}

.modal-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 24px 24px 24px;
  border-top: 1px solid var(--border-color);
}

.cancel-btn {
  padding: 10px 20px;
  border: 1px solid var(--border-color);
  background: white;
  color: var(--text-secondary);
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.cancel-btn:hover {
  border-color: var(--text-secondary);
  color: var(--text-primary);
}

.confirm-btn {
  padding: 10px 20px;
  border: none;
  background: var(--primary-red);
  color: white;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.confirm-btn:hover:not(:disabled) {
  background: var(--primary-red-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(211, 47, 47, 0.3);
}

.confirm-btn:disabled {
  background: var(--text-light);
  cursor: not-allowed;
}

/* 动画 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes selectPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.02); }
  100% { transform: scale(1); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .role-selector-modal {
    width: 95%;
    max-height: 85vh;
  }
  
  .modal-header {
    padding: 20px 20px 16px 20px;
  }
  
  .modal-title {
    font-size: 18px;
  }
  
  .modal-body {
    padding: 20px;
  }
  
  .role-card {
    padding: 16px;
    gap: 12px;
  }
  
  .role-avatar {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }
  
  .role-name {
    font-size: 16px;
  }
  
  .role-description {
    font-size: 13px;
  }
  
  .role-example {
    font-size: 12px;
    padding: 6px 10px;
  }
  
  .modal-footer {
    padding: 16px 20px 20px 20px;
  }
}

@media (max-width: 480px) {
  .role-card {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }
  
  .role-avatar {
    align-self: center;
  }
  
  .role-status {
    position: static;
    align-self: center;
    margin-top: 8px;
  }
  
  .modal-footer {
    flex-direction: column;
    gap: 8px;
  }
  
  .cancel-btn,
  .confirm-btn {
    width: 100%;
    padding: 12px;
  }
}
</style>
