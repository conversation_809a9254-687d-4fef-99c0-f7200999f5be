# 成都开放大学 - 思政AI对话平台

## 项目简介

这是一个融合AI大模型与思政教材知识库的对话网站程序。该平台能够智能回答用户问题，并从内置的思政教材中提炼知识点，提供原文参考。

## 功能特性

### 🎓 智能知识库系统
- 预装高等教育出版社2023年版四大思政教材电子知识库
- 包括《中国近现代史纲要》《思想道德与法治》《马克思主义基本原理》《毛泽东思想和中国特色社会主义理论体系概论》
- 支持教材内容的智能问答

### 👥 多角色对话模式
- **导师**：专注于从知识库中查找对应的原文及出处，提供权威解答
- **知识点**：提取并罗列知识库中的相关知识点，简明扼要
- **学霸**：根据用户问题提供全面深入的回答，结合教材知识进行解析
- **研究员**：客观评价社会资讯，提供理性分析（不依赖知识库）
- **鲁迅**：模仿鲁迅先生的语言风格，对社会现象进行犀利点评（不依赖知识库）

### 🔍 精准知识定位
- 针对每个问答结果，系统提供知识点在教材中的精确章节定位
- 展示相关原文内容
- 支持原文内容复制功能

### 💡 知识点提取功能
- 支持独立的知识点提取模式
- 可根据用户提问，精准罗列问题涉及的核心知识点条目

## 技术架构

- **前端框架**：Vue 3（选项式API风格）
- **路由管理**：Vue Router
- **开发语言**：JavaScript
- **图标库**：Font Awesome
- **数据存储**：localStorage（用于记录用户配置信息）
- **构建工具**：Vite
- **样式**：原生CSS + CSS变量

## 项目结构

```
src/
├── components/          # 组件目录
│   ├── ErrorBoundary.vue    # 错误边界组件
│   ├── LoadingSpinner.vue   # 加载组件
│   ├── MessageItem.vue      # 消息项组件
│   ├── RoleSelector.vue     # 角色选择器
│   └── SourceModal.vue      # 教材原文弹窗
├── views/              # 页面目录
│   ├── Welcome.vue          # 欢迎页面
│   └── Chat.vue             # 对话页面
├── router/             # 路由配置
│   └── index.js
├── utils/              # 工具函数
│   ├── api.js              # API接口
│   └── index.js            # 通用工具函数
├── assets/             # 静态资源
│   └── css/
│       └── global.css      # 全局样式
├── App.vue             # 根组件
└── main.js             # 入口文件
```

## 开发指南

### 环境要求
- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 预览生产版本
```bash
npm run preview
```

## API接口

### 基本信息
- **接口地址**：https://api.chatmarx.zihaoai.cn/chat
- **请求方式**：POST
- **Content-Type**：application/json

### 请求参数
```json
{
    "template": "角色名称",  // 可选值：导师、知识点、学霸、研究员、鲁迅
    "question": "用户输入的问题"
}
```

### 响应参数
```json
{
    "result": "AI的回答内容（支持Markdown格式）",
    "sources": [
        {
            "name": "教材名称",
            "content": "原文内容"
        }
    ]
}
```

## 设计特色

### 视觉风格
- 融合思政红色元素与AI科技感
- 体现思政教育与现代技术的结合
- 动态CSS背景，包含党徽、五星红旗等思政元素
- 数据流、神经网络等AI科技视觉效果

### 响应式设计
- **PC端**（≥1200px）：完整功能展示
- **平板端**（768px-1199px）：优化布局适配
- **手机端**（<768px）：移动端友好的触控体验

### 用户体验
- 界面美观大方，交互流畅自然
- 操作简单直观，学习成本低
- 支持键盘快捷键操作
- 完善的加载状态和错误处理

## 浏览器兼容性

- Chrome >= 88
- Firefox >= 85
- Safari >= 14
- Edge >= 88

## 许可证

本项目仅供成都开放大学内部使用。

## 更新日志

### v1.0.0 (2024-08-12)
- 🎉 项目初始版本发布
- ✨ 实现欢迎页面和对话界面
- ✨ 支持5种角色对话模式
- ✨ 集成教材原文展示功能
- ✨ 完整的响应式设计
- 🐛 修复已知问题并优化性能
