{"name": "marked", "description": "A markdown parser built for speed", "author": "<PERSON>", "version": "5.1.2", "type": "module", "main": "./lib/marked.cjs", "module": "./lib/marked.esm.js", "browser": "./lib/marked.umd.js", "bin": {"marked": "bin/marked.js"}, "man": "./man/marked.1", "files": ["bin/", "lib/", "src/", "man/", "marked.min.js"], "exports": {".": {"import": "./lib/marked.esm.js", "default": "./lib/marked.cjs"}, "./bin/marked": "./bin/marked.js", "./package.json": "./package.json"}, "repository": "git://github.com/markedjs/marked.git", "homepage": "https://marked.js.org", "bugs": {"url": "http://github.com/markedjs/marked/issues"}, "license": "MIT", "keywords": ["markdown", "markup", "html"], "tags": ["markdown", "markup", "html"], "devDependencies": {"@babel/core": "^7.22.9", "@babel/preset-env": "^7.22.9", "@markedjs/html-differ": "^4.0.2", "@rollup/plugin-babel": "^6.0.3", "@semantic-release/commit-analyzer": "^10.0.1", "@semantic-release/git": "^10.0.1", "@semantic-release/github": "^9.0.4", "@semantic-release/npm": "^10.0.4", "@semantic-release/release-notes-generator": "^11.0.4", "cheerio": "^1.0.0-rc.12", "commonmark": "0.30.0", "eslint": "^8.45.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-n": "^16.0.1", "eslint-plugin-promise": "^6.1.1", "front-matter": "^4.0.2", "highlight.js": "^11.8.0", "jasmine": "^5.1.0", "markdown-it": "13.0.1", "node-fetch": "^3.3.1", "recheck": "^4.4.5", "rollup": "^3.26.3", "semantic-release": "^21.0.7", "titleize": "^3.0.0", "uglify-js": "^3.17.4", "vuln-regex-detector": "^1.3.0"}, "scripts": {"test": "jasmine --config=jasmine.json", "test:all": "npm test && npm run test:lint", "test:unit": "npm test -- test/unit/**/*-spec.js", "test:specs": "npm test -- test/specs/**/*-spec.js", "test:lint": "eslint .", "test:redos": "node test/recheck.js > vuln.js", "test:update": "node test/update-specs.js", "rules": "node test/rules.js", "bench": "npm run rollup && node test/bench.js", "lint": "eslint --fix .", "build:reset": "git checkout upstream/master lib/marked.cjs lib/marked.umd.js lib/marked.esm.js marked.min.js", "build": "npm run rollup && npm run minify", "build:docs": "node build-docs.js", "rollup": "rollup -c rollup.config.js", "minify": "uglifyjs lib/marked.umd.js -cm  --comments /Copyright/ -o marked.min.js"}, "engines": {"node": ">= 16"}}